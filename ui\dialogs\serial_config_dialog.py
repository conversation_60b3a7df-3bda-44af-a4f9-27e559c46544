#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
串口配置对话框
Serial Configuration Dialog
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QComboBox, QSpinBox, QCheckBox, QGroupBox, QMessageBox,
    QTextEdit, QProgressBar
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from config.settings import settings


class SerialDetectionWorker(QThread):
    """串口检测工作线程"""
    ports_detected = pyqtSignal(list)  # 检测到的端口列表
    progress_updated = pyqtSignal(int)  # 进度更新
    status_updated = pyqtSignal(str)   # 状态更新
    
    def run(self):
        """运行串口检测"""
        try:
            import serial.tools.list_ports
            import serial
            import time
            import re
            
            # 获取所有可用串口
            all_ports = list(serial.tools.list_ports.comports())
            self.status_updated.emit(f"发现 {len(all_ports)} 个串口，正在检测...")
            
            weight_scale_ports = []
            
            for i, port in enumerate(all_ports):
                self.progress_updated.emit(int((i + 1) / len(all_ports) * 100))
                self.status_updated.emit(f"检测端口 {port.device}...")
                
                try:
                    # 尝试连接端口
                    test_serial = serial.Serial(
                        port=port.device,
                        baudrate=9600,
                        timeout=0.5
                    )
                    
                    # 等待数据
                    time.sleep(1.5)
                    
                    if test_serial.in_waiting > 0:
                        # 读取数据
                        data = test_serial.read(test_serial.in_waiting).decode('utf-8', errors='ignore')
                        
                        # 检查是否包含重量数据格式
                        if re.search(r'[sw][gn](-?\d+\.?\d*)kg', data, re.IGNORECASE):
                            weight_scale_ports.append({
                                'device': port.device,
                                'description': port.description,
                                'data_sample': data.strip()[:50]  # 前50个字符作为样本
                            })
                    
                    test_serial.close()
                    
                except Exception:
                    # 连接失败，继续下一个
                    continue
            
            self.ports_detected.emit(weight_scale_ports)
            
        except ImportError:
            self.status_updated.emit("错误: 未安装pyserial库")
        except Exception as e:
            self.status_updated.emit(f"检测失败: {str(e)}")


class SerialConfigDialog(QDialog):
    """串口配置对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.detection_worker = None
        self.init_ui()
        self.load_current_settings()
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("串口配置")
        self.setFixedSize(500, 600)
        self.setModal(True)
        
        layout = QVBoxLayout(self)
        
        # 基本配置组
        basic_group = QGroupBox("基本配置")
        basic_layout = QVBoxLayout(basic_group)
        
        # 串口选择
        port_layout = QHBoxLayout()
        port_layout.addWidget(QLabel("串口:"))
        self.port_combo = QComboBox()
        self.port_combo.setEditable(True)
        port_layout.addWidget(self.port_combo)
        
        self.refresh_ports_btn = QPushButton("刷新")
        self.refresh_ports_btn.clicked.connect(self.refresh_ports)
        port_layout.addWidget(self.refresh_ports_btn)
        basic_layout.addLayout(port_layout)
        
        # 波特率
        baudrate_layout = QHBoxLayout()
        baudrate_layout.addWidget(QLabel("波特率:"))
        self.baudrate_combo = QComboBox()
        self.baudrate_combo.addItems(["9600", "19200", "38400", "57600", "115200"])
        self.baudrate_combo.setEditable(True)
        baudrate_layout.addWidget(self.baudrate_combo)
        basic_layout.addLayout(baudrate_layout)
        
        # 超时时间
        timeout_layout = QHBoxLayout()
        timeout_layout.addWidget(QLabel("超时时间(秒):"))
        self.timeout_spin = QSpinBox()
        self.timeout_spin.setRange(1, 10)
        timeout_layout.addWidget(self.timeout_spin)
        basic_layout.addLayout(timeout_layout)
        
        # 自动检测
        self.auto_detect_check = QCheckBox("启用自动检测重量秤设备")
        basic_layout.addWidget(self.auto_detect_check)
        
        layout.addWidget(basic_group)
        
        # 检测功能组
        detection_group = QGroupBox("设备检测")
        detection_layout = QVBoxLayout(detection_group)
        
        self.detect_btn = QPushButton("检测重量秤设备")
        self.detect_btn.clicked.connect(self.start_detection)
        detection_layout.addWidget(self.detect_btn)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        detection_layout.addWidget(self.progress_bar)
        
        self.status_label = QLabel("点击检测按钮开始扫描串口")
        detection_layout.addWidget(self.status_label)
        
        self.result_text = QTextEdit()
        self.result_text.setMaximumHeight(150)
        self.result_text.setPlaceholderText("检测结果将显示在这里...")
        detection_layout.addWidget(self.result_text)
        
        layout.addWidget(detection_group)
        
        # 按钮组
        button_layout = QHBoxLayout()
        
        self.test_btn = QPushButton("测试连接")
        self.test_btn.clicked.connect(self.test_connection)
        button_layout.addWidget(self.test_btn)
        
        button_layout.addStretch()
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        self.save_btn = QPushButton("保存")
        self.save_btn.clicked.connect(self.save_settings)
        button_layout.addWidget(self.save_btn)
        
        layout.addLayout(button_layout)
        
        # 初始化端口列表
        self.refresh_ports()
    
    def refresh_ports(self):
        """刷新串口列表"""
        try:
            import serial.tools.list_ports
            
            self.port_combo.clear()
            ports = list(serial.tools.list_ports.comports())
            
            for port in ports:
                self.port_combo.addItem(f"{port.device} - {port.description}", port.device)
                
        except ImportError:
            QMessageBox.warning(self, "警告", "未安装pyserial库，无法获取串口列表")
    
    def load_current_settings(self):
        """加载当前设置"""
        # 设置当前配置值
        current_port = settings.serial_port
        current_baudrate = str(settings.serial_baudrate)
        current_timeout = settings.serial_timeout
        current_auto_detect = settings.serial_auto_detect
        
        # 查找并设置端口
        for i in range(self.port_combo.count()):
            if self.port_combo.itemData(i) == current_port:
                self.port_combo.setCurrentIndex(i)
                break
        else:
            # 如果没找到，直接设置文本
            self.port_combo.setCurrentText(current_port)
        
        # 设置波特率
        index = self.baudrate_combo.findText(current_baudrate)
        if index >= 0:
            self.baudrate_combo.setCurrentIndex(index)
        else:
            self.baudrate_combo.setCurrentText(current_baudrate)
        
        # 设置超时时间
        self.timeout_spin.setValue(current_timeout)
        
        # 设置自动检测
        self.auto_detect_check.setChecked(current_auto_detect)
    
    def start_detection(self):
        """开始检测设备"""
        if self.detection_worker and self.detection_worker.isRunning():
            return
        
        self.detect_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.result_text.clear()
        
        self.detection_worker = SerialDetectionWorker()
        self.detection_worker.ports_detected.connect(self.on_ports_detected)
        self.detection_worker.progress_updated.connect(self.progress_bar.setValue)
        self.detection_worker.status_updated.connect(self.status_label.setText)
        self.detection_worker.finished.connect(self.on_detection_finished)
        self.detection_worker.start()
    
    def on_ports_detected(self, ports):
        """处理检测到的端口"""
        if not ports:
            self.result_text.setText("未检测到重量秤设备")
            return
        
        result_text = f"检测到 {len(ports)} 个重量秤设备:\n\n"
        
        for port in ports:
            result_text += f"端口: {port['device']}\n"
            result_text += f"描述: {port['description']}\n"
            result_text += f"数据样本: {port['data_sample']}\n"
            result_text += "-" * 40 + "\n"
        
        self.result_text.setText(result_text)
        
        # 自动选择第一个检测到的端口
        if ports:
            first_port = ports[0]['device']
            self.port_combo.setCurrentText(first_port)
    
    def on_detection_finished(self):
        """检测完成"""
        self.detect_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.status_label.setText("检测完成")
    
    def test_connection(self):
        """测试连接"""
        try:
            import serial
            
            port = self.port_combo.currentText().split(' - ')[0] if ' - ' in self.port_combo.currentText() else self.port_combo.currentText()
            baudrate = int(self.baudrate_combo.currentText())
            timeout = self.timeout_spin.value()
            
            test_serial = serial.Serial(
                port=port,
                baudrate=baudrate,
                timeout=timeout
            )
            
            test_serial.close()
            QMessageBox.information(self, "成功", f"串口 {port} 连接测试成功！")
            
        except ImportError:
            QMessageBox.warning(self, "错误", "未安装pyserial库")
        except Exception as e:
            QMessageBox.warning(self, "连接失败", f"无法连接到串口: {str(e)}")
    
    def save_settings(self):
        """保存设置"""
        try:
            port = self.port_combo.currentText().split(' - ')[0] if ' - ' in self.port_combo.currentText() else self.port_combo.currentText()
            baudrate = int(self.baudrate_combo.currentText())
            timeout = self.timeout_spin.value()
            auto_detect = self.auto_detect_check.isChecked()
            
            # 保存到配置
            settings.set('serial.port', port)
            settings.set('serial.baudrate', baudrate)
            settings.set('serial.timeout', timeout)
            settings.set('serial.auto_detect', auto_detect)
            settings.save_config()
            
            QMessageBox.information(self, "成功", "串口配置已保存！\n重启应用后生效。")
            self.accept()
            
        except ValueError:
            QMessageBox.warning(self, "错误", "波特率必须是数字")
        except Exception as e:
            QMessageBox.warning(self, "保存失败", f"保存配置失败: {str(e)}")
