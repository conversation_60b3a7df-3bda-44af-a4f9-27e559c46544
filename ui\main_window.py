#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主界面窗口
Main Window
"""

from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QFrame, QStackedWidget,
    QScrollArea, QGridLayout, QSpacerItem, QSizePolicy
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QIcon, QKeySequence, QShortcut

try:
    from .styles import GlassmorphismStyles
except ImportError:
    from ui.styles import GlassmorphismStyles

try:
    from config.settings import settings
except ImportError:
    # 创建默认设置
    class Settings:
        app_name = "智慧食堂管理系统"
        fullscreen = True  # 默认全屏显示
        window_width = 1200
        window_height = 800
    settings = Settings()

class NavigationButton(QPushButton):
    """导航按钮类"""
    
    def __init__(self, text: str, icon_text: str = ""):
        super().__init__()
        self.setText(text)
        self.icon_text = icon_text
        self.setObjectName("navButton")
        self.setFixedHeight(60)
        self.setCheckable(True)
        
        # 应用样式
        self.setStyleSheet("""
        QPushButton#navButton {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 12px 20px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
            font-size: 14px;
            text-align: left;
        }
        QPushButton#navButton:hover {
            background: rgba(255, 255, 255, 0.15);
            color: white;
        }
        QPushButton#navButton:checked {
            background: rgba(147, 51, 234, 0.2);
            border: 1px solid rgba(147, 51, 234, 0.3);
            color: white;
        }
        """)

class FunctionCard(QFrame):
    """功能卡片类"""
    
    clicked = pyqtSignal(str)
    
    def __init__(self, title: str, description: str, icon_text: str = "📋"):
        super().__init__()
        self.title = title
        self.description = description
        self.icon_text = icon_text
        
        self.setObjectName("functionCard")
        self.setFixedSize(280, 160)
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        
        # 创建布局
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 图标
        icon_label = QLabel(icon_text)
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label.setStyleSheet("""
        QLabel {
            font-size: 32px;
            color: rgba(147, 51, 234, 0.8);
            background: transparent;
            border: none;
        }
        """)
        
        # 标题
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
        QLabel {
            font-size: 16px;
            font-weight: 600;
            color: white;
            background: transparent;
            border: none;
        }
        """)
        
        # 描述
        desc_label = QLabel(description)
        desc_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        desc_label.setWordWrap(True)
        desc_label.setStyleSheet("""
        QLabel {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            background: transparent;
            border: none;
        }
        """)
        
        layout.addWidget(icon_label)
        layout.addWidget(title_label)
        layout.addWidget(desc_label)
        
        # 应用卡片样式
        self.setStyleSheet("""
        QFrame#functionCard {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
        }
        QFrame#functionCard:hover {
            background: rgba(255, 255, 255, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        """)
    
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.clicked.emit(self.title)
        super().mousePressEvent(event)

class MainWindow(QMainWindow):
    """主界面窗口类"""
    
    def __init__(self, access_token: str = None):
        super().__init__()
        self.access_token = access_token
        
        self.setWindowTitle(settings.app_name)
        
        # 根据配置设置窗口大小
        if settings.fullscreen:
            # 使用真正的全屏模式（无标题栏和边框）
            self.showFullScreen()
        else:
            self.setFixedSize(settings.window_width, settings.window_height)
        
        # 初始化UI
        self.init_ui()
        self.apply_styles()

        # 设置快捷键
        self.setup_shortcuts()

        # 居中显示
        if not settings.fullscreen:
            self.center_window()

    def setup_shortcuts(self):
        """设置快捷键"""
        # ESC键退出全屏
        if settings.fullscreen:
            escape_shortcut = QShortcut(QKeySequence(Qt.Key.Key_Escape), self)
            escape_shortcut.activated.connect(self.toggle_fullscreen)

        # F11键切换全屏
        f11_shortcut = QShortcut(QKeySequence(Qt.Key.Key_F11), self)
        f11_shortcut.activated.connect(self.toggle_fullscreen)

    def toggle_fullscreen(self):
        """切换全屏模式"""
        if self.isFullScreen():
            self.showNormal()
            # 恢复到配置的窗口大小
            self.setFixedSize(settings.window_width, settings.window_height)
            self.center_window()
        else:
            self.showFullScreen()

    def init_ui(self):
        """初始化用户界面"""
        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 创建侧边栏
        self.create_sidebar(main_layout)
        
        # 创建主内容区域
        self.create_main_content(main_layout)
    
    def create_sidebar(self, parent_layout):
        """创建侧边栏"""
        sidebar = QFrame()
        sidebar.setObjectName("sidebar")
        sidebar.setFixedWidth(280)
        
        sidebar_layout = QVBoxLayout(sidebar)
        sidebar_layout.setSpacing(15)
        sidebar_layout.setContentsMargins(20, 30, 20, 30)
        
        # 标题区域
        title_label = QLabel(settings.app_name)
        title_label.setObjectName("sidebarTitle")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 用户信息
        user_info = QLabel("欢迎使用智慧食堂系统")
        user_info.setObjectName("userInfo")
        user_info.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        sidebar_layout.addWidget(title_label)
        sidebar_layout.addWidget(user_info)
        sidebar_layout.addSpacing(20)
        
        # 导航按钮
        nav_buttons = [
            ("🏠 首页", "home"),
            ("📋 食谱管理", "recipe"),
            ("🛒 采购下单", "purchase"),
            ("📦 订单管理", "orders"),
            ("🏪 仓储管理", "warehouse"),
            ("📊 上报管理", "reports"),
            ("❓ 答疑解惑", "help"),
            ("📱 码上举报", "qr_report"),
            ("📺 大屏展示", "dashboard")
        ]
        
        self.nav_buttons = {}
        for text, key in nav_buttons:
            btn = NavigationButton(text)
            btn.clicked.connect(lambda checked, k=key: self.switch_page(k))
            self.nav_buttons[key] = btn
            sidebar_layout.addWidget(btn)
        
        # 设置首页为默认选中
        self.nav_buttons["home"].setChecked(True)
        
        sidebar_layout.addStretch()

        # 设置按钮
        settings_btn = QPushButton("⚙️ 系统设置")
        settings_btn.setObjectName("settingsButton")
        settings_btn.clicked.connect(self.open_settings)
        sidebar_layout.addWidget(settings_btn)

        # 退出按钮
        logout_btn = QPushButton("退出登录")
        logout_btn.setObjectName("logoutButton")
        logout_btn.clicked.connect(self.logout)
        sidebar_layout.addWidget(logout_btn)
        
        parent_layout.addWidget(sidebar)
    
    def create_main_content(self, parent_layout):
        """创建主内容区域"""
        # 主内容容器
        content_container = QFrame()
        content_container.setObjectName("contentContainer")
        
        content_layout = QVBoxLayout(content_container)
        content_layout.setContentsMargins(30, 30, 30, 30)
        content_layout.setSpacing(20)
        
        # 页面标题
        self.page_title = QLabel("系统首页")
        self.page_title.setObjectName("pageTitle")
        
        # 堆叠窗口用于切换不同页面
        self.stacked_widget = QStackedWidget()
        
        # 创建各个页面
        self.create_home_page()
        self.create_placeholder_pages()
        
        content_layout.addWidget(self.page_title)
        content_layout.addWidget(self.stacked_widget)
        
        parent_layout.addWidget(content_container)
    
    def create_home_page(self):
        """创建首页"""
        home_page = QWidget()
        home_layout = QVBoxLayout(home_page)
        home_layout.setSpacing(30)
        
        # 欢迎信息
        welcome_label = QLabel("欢迎使用智慧食堂管理系统")
        welcome_label.setObjectName("welcomeLabel")
        welcome_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 功能卡片网格
        cards_container = QWidget()
        cards_layout = QGridLayout(cards_container)
        cards_layout.setSpacing(20)
        
        # 功能卡片数据
        function_cards = [
            ("食谱管理", "管理每日食谱和营养搭配", "🍽️"),
            ("采购下单", "食材采购和供应商管理", "🛒"),
            ("订单管理", "订单跟踪和收货确认", "📦"),
            ("仓储管理", "库存管理和出入库记录", "🏪"),
            ("上报管理", "数据统计和报表生成", "📊"),
            ("答疑解惑", "在线客服和问题解答", "❓"),
            ("码上举报", "二维码扫描举报功能", "📱"),
            ("大屏展示", "数据可视化展示", "📺")
        ]
        
        row, col = 0, 0
        for title, desc, icon in function_cards:
            card = FunctionCard(title, desc, icon)
            card.clicked.connect(self.handle_card_click)
            cards_layout.addWidget(card, row, col)
            
            col += 1
            if col >= 3:  # 每行3个卡片
                col = 0
                row += 1
        
        home_layout.addWidget(welcome_label)
        home_layout.addWidget(cards_container)
        home_layout.addStretch()
        
        self.stacked_widget.addWidget(home_page)
    
    def create_placeholder_pages(self):
        """创建占位页面"""
        # 导入食谱管理模块
        from .modules.recipe_module import RecipeModule
        from .modules.order_module import OrderModule
        from .modules.weight_submission_module import WeightSubmissionModule
        from api.canteen_api import CanteenAPI
        from api.order_api import OrderAPI
        from api.weight_api import WeightAPI

        # 创建API实例
        api = CanteenAPI(settings.api_base_url) if self.access_token else None
        order_api = OrderAPI(settings.api_base_url) if self.access_token else None
        weight_api = WeightAPI(settings.api_base_url) if self.access_token else None

        if api and self.access_token:
            api.set_access_token(self.access_token)
        if order_api and self.access_token:
            order_api.set_access_token(self.access_token)
        if weight_api and self.access_token:
            weight_api.set_access_token(self.access_token)

        # 食谱管理页面
        recipe_page = RecipeModule(api)
        self.stacked_widget.addWidget(recipe_page)

        # 订单管理页面
        self.order_page = OrderModule()
        if order_api:
            self.order_page.set_api(order_api)
        self.stacked_widget.addWidget(self.order_page)

        # 重量提交页面
        try:
            self.weight_submission_page = WeightSubmissionModule()
            if weight_api:
                self.weight_submission_page.set_api(weight_api)
            self.stacked_widget.addWidget(self.weight_submission_page)
            print("✅ 重量提交页面初始化成功")
        except Exception as e:
            print(f"❌ 重量提交页面初始化失败: {e}")
            # 创建一个占位页面
            placeholder = QWidget()
            layout = QVBoxLayout(placeholder)
            label = QLabel(f"重量提交功能初始化失败:\n{str(e)}")
            label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            label.setStyleSheet("color: white; font-size: 16px;")
            layout.addWidget(label)
            self.weight_submission_page = placeholder
            self.stacked_widget.addWidget(self.weight_submission_page)

        # 其他占位页面
        other_pages = [
            "purchase", "warehouse",
            "reports", "help", "qr_report", "dashboard"
        ]

        for page_key in other_pages:
            page = QWidget()
            layout = QVBoxLayout(page)

            label = QLabel(f"{page_key.upper()} 功能正在开发中...")
            label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                color: rgba(255, 255, 255, 0.7);
                background: transparent;
                border: none;
            }
            """)

            layout.addWidget(label)
            self.stacked_widget.addWidget(page)

    def apply_styles(self):
        """应用样式"""
        # 主窗口背景
        self.setStyleSheet(GlassmorphismStyles.get_main_window_style())

        # 侧边栏样式
        sidebar_style = """
        QFrame#sidebar {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
        }
        """

        # 标题样式
        title_style = """
        QLabel#sidebarTitle {
            font-size: 18px;
            font-weight: 700;
            color: white;
            background: transparent;
            border: none;
        }
        QLabel#userInfo {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            background: transparent;
            border: none;
        }
        """

        # 内容容器样式
        content_style = """
        QFrame#contentContainer {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
        }
        """

        # 页面标题样式
        page_title_style = """
        QLabel#pageTitle {
            font-size: 24px;
            font-weight: 700;
            color: white;
            background: transparent;
            border: none;
        }
        """

        # 欢迎标签样式
        welcome_style = """
        QLabel#welcomeLabel {
            font-size: 20px;
            font-weight: 600;
            color: white;
            background: transparent;
            border: none;
            padding: 20px;
        }
        """

        # 退出按钮样式
        logout_style = """
        QPushButton#logoutButton {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid rgba(239, 68, 68, 0.3);
            border-radius: 12px;
            padding: 12px 20px;
            color: white;
            font-weight: 500;
            font-size: 14px;
        }
        QPushButton#logoutButton:hover {
            background: rgba(239, 68, 68, 0.3);
        }
        """

        # 应用所有样式
        combined_style = (sidebar_style + title_style + content_style +
                         page_title_style + welcome_style + logout_style)

        # 查找并应用样式到对应组件
        for widget in self.findChildren(QWidget):
            if widget.objectName():
                widget.setStyleSheet(combined_style)

    def center_window(self):
        """居中显示窗口"""
        from PyQt6.QtWidgets import QApplication
        screen = QApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)

    def switch_page(self, page_key: str):
        """切换页面"""
        # 更新导航按钮状态
        for key, btn in self.nav_buttons.items():
            btn.setChecked(key == page_key)

        # 更新页面标题
        page_titles = {
            "home": "系统首页",
            "recipe": "食谱管理",
            "purchase": "采购下单",
            "orders": "订单管理",
            "weight_submission": "商品重量提交",
            "warehouse": "仓储管理",
            "reports": "上报管理",
            "help": "答疑解惑",
            "qr_report": "码上举报",
            "dashboard": "大屏展示"
        }

        self.page_title.setText(page_titles.get(page_key, "未知页面"))

        # 切换到对应页面
        page_indices = {
            "home": 0, "recipe": 1, "orders": 2, "weight_submission": 3, "purchase": 4,
            "warehouse": 5, "reports": 6, "help": 7,
            "qr_report": 8, "dashboard": 9
        }

        index = page_indices.get(page_key, 0)
        self.stacked_widget.setCurrentIndex(index)

    def handle_card_click(self, card_title: str):
        """处理功能卡片点击"""
        # 根据卡片标题映射到页面
        card_to_page = {
            "食谱管理": "recipe",
            "采购下单": "purchase",
            "订单管理": "orders",
            "仓储管理": "warehouse",
            "上报管理": "reports",
            "答疑解惑": "help",
            "码上举报": "qr_report",
            "大屏展示": "dashboard"
        }

        page_key = card_to_page.get(card_title)
        if page_key:
            self.switch_page(page_key)

    def open_weight_submission_page(self, product_data: dict):
        """打开重量提交页面"""
        try:
            print(f"🔄 正在打开重量提交页面，商品: {product_data.get('name', '未知商品')}")

            # 检查重量提交页面是否存在
            if not hasattr(self, 'weight_submission_page'):
                raise Exception("重量提交页面未初始化")

            # 设置商品信息到重量提交页面
            if hasattr(self.weight_submission_page, 'set_product_info'):
                self.weight_submission_page.set_product_info(product_data)
                print("✅ 商品信息设置成功")
            else:
                print("❌ 重量提交页面没有set_product_info方法")

            # 切换到重量提交页面
            self.switch_page("weight_submission")
            print("✅ 页面切换成功")

        except Exception as e:
            print(f"❌ 打开重量提交页面失败: {e}")
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.warning(self, "错误", f"打开重量提交页面失败: {str(e)}")

    def logout(self):
        """退出登录"""
        from PyQt6.QtWidgets import QMessageBox
        from PyQt6.QtCore import QTimer
        from utils.auth_manager import auth_manager

        reply = QMessageBox.question(
            self, "确认退出", "确定要退出登录吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 清除登录信息
            self.access_token = None

            # 清除认证管理器中的认证信息
            auth_manager.clear_current_auth()

            # 先创建登录窗口，跳过token检查
            from .login_window import LoginWindow
            self.login_window = LoginWindow(skip_token_check=True)
            self.login_window.show()

            # 延迟关闭主窗口，确保登录窗口已经完全显示
            QTimer.singleShot(100, self._delayed_close)

    def open_settings(self):
        """打开设置对话框"""
        from PyQt6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTabWidget, QWidget

        # 创建设置对话框
        settings_dialog = QDialog(self)
        settings_dialog.setWindowTitle("系统设置")
        settings_dialog.setFixedSize(600, 400)
        settings_dialog.setModal(True)

        layout = QVBoxLayout(settings_dialog)

        # 创建选项卡
        tab_widget = QTabWidget()

        # 串口设置选项卡
        serial_tab = QWidget()
        serial_layout = QVBoxLayout(serial_tab)

        serial_config_btn = QPushButton("串口配置")
        serial_config_btn.clicked.connect(self.open_serial_config)
        serial_layout.addWidget(serial_config_btn)

        serial_layout.addStretch()
        tab_widget.addTab(serial_tab, "串口设置")

        # 其他设置选项卡可以在这里添加

        layout.addWidget(tab_widget)

        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(settings_dialog.accept)
        button_layout.addWidget(close_btn)

        layout.addLayout(button_layout)

        settings_dialog.exec()

    def open_serial_config(self):
        """打开串口配置对话框"""
        try:
            from ui.dialogs.serial_config_dialog import SerialConfigDialog

            dialog = SerialConfigDialog(self)
            dialog.exec()

        except ImportError as e:
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.warning(self, "错误", f"无法打开串口配置: {str(e)}")

    def _delayed_close(self):
        """延迟关闭主窗口"""
        self.close()

    def closeEvent(self, event):
        """窗口关闭事件"""
        # 如果是正常关闭（不是退出登录），退出应用程序
        if not hasattr(self, 'login_window'):
            from PyQt6.QtWidgets import QApplication
            QApplication.quit()
        event.accept()

    def paintEvent(self, event):
        """绘制背景"""
        from PyQt6.QtGui import QPainter, QLinearGradient, QColor, QBrush

        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # 创建渐变背景
        gradient = QLinearGradient(0, 0, self.width(), self.height())
        gradient.setColorAt(0, QColor(30, 30, 60))
        gradient.setColorAt(0.5, QColor(60, 30, 90))
        gradient.setColorAt(1, QColor(90, 60, 120))

        painter.fillRect(self.rect(), QBrush(gradient))
